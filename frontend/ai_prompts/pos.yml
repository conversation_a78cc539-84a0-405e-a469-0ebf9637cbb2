GeneratePOSAPPPages:
  description: |
    In the `frontend/` project, generate CRUD components for the [pos](cci:7://file:///home/<USER>/Documents/GitHub/elquds_erp/pos:0:0-0:0) feature using the 'pos/', 'sessions/', and 'session/<id>/transactions/' APIs.
    You should use "react-bootstrap" to create the UI, similar to the "inventory-counts/" pages.
  rules:
    - Create service file `src/services/posService.js` with the following API functions:
        - `getPOSList(params)` - GET /pos/
        - `getPOSDetails(id)` - GET /pos/{id}/
        - `startSession(posId, data)` - POST /pos/{id}/start_session/
        - `getSessions(params)` - GET /sessions/
        - `getSessionDetails(id)` - GET /sessions/{id}/
        - `closeSession(id, data)` - POST /sessions/{id}/close/
        - `suspendSession(id)` - POST /sessions/{id}/suspend/
        - `resumeSession(id)` - POST /sessions/{id}/resume/
        - `getSessionTransactions(sessionId, params)` - GET /session/{id}/transactions/

    - Create components in `src/components/pos/`:
        - `POSList.jsx` - Lists all POS terminals with action buttons
        - `POSSessionList.jsx` - Lists all sessions with filters and actions
        - `POSSessionDetail.jsx` - Shows session details and transactions
        - `POSSessionForm.jsx` - Form for creating/editing sessions
        - `DeleteSessionModal.jsx` - Confirmation dialog for deleting sessions
        - `SessionStatusBadge.jsx` - Displays status with appropriate styling
        - `TransactionTypeBadge.jsx` - Displays transaction type with styling

    - Each component should:
        - Be a functional component using React hooks
        - Use React Bootstrap for UI components
        - Follow the same styling and patterns as existing components
        - Include PropTypes validation
        - Have JSDoc comments for documentation
        - Handle loading and error states
        - Implement proper form validation

    - Required features:
        - List view for POS terminals with search and filter
        - List view for sessions with date range filtering
        - Detailed view for sessions with transaction list
        - Create/Edit forms for sessions
        - Status management for sessions (open, close, suspend, resume)
        - Responsive design for all screen sizes

    - State Management:
        - Use React Query for server state
        - Local component state for UI state
        - Context API for global state if needed
        - Formik for form state management
        - Yup for form validation

    - Navigation:
        - Integrate with existing routing system
        - Add links in the main navigation if needed
        - Include breadcrumbs for better navigation

    - Testing:
        - Create corresponding test files in `src/components/__tests__/`
        - Test component rendering and interactions
        - Test API service functions

    - Error Handling:
        - Display user-friendly error messages
        - Handle network errors gracefully
        - Show loading states during API calls
        - Implement proper error boundaries

    - Dependencies:
        - react-bootstrap
        - react-query
        - formik
        - yup
        - date-fns (for date handling)
        - react-icons (for icons)


  Based on the implementation patterns documented in "PRODUCTS_IMPLEMENTATION_SUMMARY.md", "INVENTORY_COUNTS_IMPLEMENTATION_ANALYSIS.md", and "CATEGORIES_IMPLEMENTATION_SUMMARY.md", generate complete CRUD components for the POS (Point of Sale) feature in the `frontend/` project.

**Objective** :
  Create a comprehensive POS management system with the following components:

  **API Endpoints to Use**:
          - `pos/` - For POS terminal management
          - `sessions/` - For POS session management
          - `session/<id>/transactions/` - For transaction management within sessions

  **Required Components**:
1. **POS Terminal Management**:
  - `POSList.jsx` - List all POS terminals with search, filter, and pagination
  - `POSView.jsx` - Detailed view of a single POS terminal
  - `POSCreate.jsx` - Form to create new POS terminals
  - `POSEdit.jsx` - Form to edit existing POS terminals
  - `DeletePOSModal.jsx` - Confirmation dialog for POS deletion

2. **Session Management**:
  - `POSSessionList.jsx` - List all sessions with filtering by date range, status, and POS terminal
  - `POSSessionDetail.jsx` - Detailed view of a session with transaction history
  - `POSSessionForm.jsx` - Form for creating/editing sessions
  - Session status management (open, close, suspend, resume)

3. **Transaction Management**:
  - Transaction list within session details
  - Transaction type badges and status indicators

  **Technical Requirements**:
                - Use **react-bootstrap** for all UI components
                - Follow the same code patterns and structure as existing components (products, inventory-counts, categories)
                - Implement proper form validation using **Formik** and **Yup**
                - Use **React Query** for server state management
                - Include comprehensive error handling and loading states
                - Ensure responsive design for all screen sizes
                - Add proper navigation integration with breadcrumbs

  **Service Layer**:
              - Create `posService.js` with all necessary API functions
              - Handle pagination, search, filtering, and sorting
              - Implement proper error handling for network requests

  **Features to Include**:
               - Search and filter functionality
               - Sortable columns with pagination
               - Status badges for sessions and transactions
               - Date range filtering for sessions
               - Action buttons for session management (start, close, suspend, resume)
               - Confirmation dialogs for destructive actions
               - Real-time status updates where applicable

  **Code Quality Standards**:
           - Follow existing naming conventions and file structure
           - Include JSDoc comments for all functions
           - Implement PropTypes validation
           - Use functional components with React hooks
           - Ensure accessibility with proper ARIA labels
           - Add comprehensive error boundaries